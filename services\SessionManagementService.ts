// Smart Office Assistant - Session Management Service
// Manages chatbot session IDs to prevent multiple sessions for the same user

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

interface UserSession {
  userId: string;
  sessionId: string;
  createdAt: number;
  lastActivity: number;
}

class SessionManagementService {
  private static instance: SessionManagementService;
  private sessions = new Map<string, UserSession>();
  private readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
  private readonly STORAGE_KEY = 'chatbot_sessions';

  private constructor() {
    this.loadSessionsFromStorage();
    
    // Clean up expired sessions periodically
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000); // Every hour
  }

  static getInstance(): SessionManagementService {
    if (!SessionManagementService.instance) {
      SessionManagementService.instance = new SessionManagementService();
    }
    return SessionManagementService.instance;
  }

  /**
   * Get existing session ID or create a new one for the user
   */
  async getOrCreateSession(userId: string): Promise<string> {
    try {
      // Check if user already has an active session
      const existingSession = this.sessions.get(userId);
      
      if (existingSession && this.isSessionValid(existingSession)) {
        // Update last activity
        existingSession.lastActivity = Date.now();
        await this.saveSessionsToStorage();
        return existingSession.sessionId;
      }

      // Create new session
      const sessionId = this.generateSessionId();
      const newSession: UserSession = {
        userId,
        sessionId,
        createdAt: Date.now(),
        lastActivity: Date.now(),
      };

      this.sessions.set(userId, newSession);
      await this.saveSessionsToStorage();

      console.log('Created new chatbot session:', { userId, sessionId });
      return sessionId;

    } catch (error) {
      console.error('Error managing session:', error);
      // Fallback: generate a temporary session ID
      return this.generateSessionId();
    }
  }

  /**
   * Update session activity timestamp
   */
  async updateSessionActivity(userId: string): Promise<void> {
    try {
      const session = this.sessions.get(userId);
      if (session) {
        session.lastActivity = Date.now();
        await this.saveSessionsToStorage();
      }
    } catch (error) {
      console.error('Error updating session activity:', error);
    }
  }

  /**
   * Get current session ID for user
   */
  getCurrentSession(userId: string): string | null {
    const session = this.sessions.get(userId);
    return session && this.isSessionValid(session) ? session.sessionId : null;
  }

  /**
   * Clear session for specific user
   */
  async clearUserSession(userId: string): Promise<void> {
    try {
      this.sessions.delete(userId);
      await this.saveSessionsToStorage();
      console.log('Cleared session for user:', userId);
    } catch (error) {
      console.error('Error clearing user session:', error);
    }
  }

  /**
   * Clear all sessions (for logout)
   */
  async clearAllSessions(): Promise<void> {
    try {
      this.sessions.clear();
      await this.saveSessionsToStorage();
      console.log('Cleared all chatbot sessions');
    } catch (error) {
      console.error('Error clearing all sessions:', error);
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
  } {
    const now = Date.now();
    let activeSessions = 0;
    let expiredSessions = 0;

    for (const session of this.sessions.values()) {
      if (this.isSessionValid(session)) {
        activeSessions++;
      } else {
        expiredSessions++;
      }
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      expiredSessions,
    };
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 15);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Check if session is still valid (not expired)
   */
  private isSessionValid(session: UserSession): boolean {
    const now = Date.now();
    return (now - session.lastActivity) < this.SESSION_TIMEOUT;
  }

  /**
   * Clean up expired sessions
   */
  private async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = Date.now();
      let cleanedCount = 0;

      for (const [userId, session] of this.sessions.entries()) {
        if (!this.isSessionValid(session)) {
          this.sessions.delete(userId);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        await this.saveSessionsToStorage();
        console.log(`Cleaned up ${cleanedCount} expired chatbot sessions`);
      }
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
    }
  }

  /**
   * Load sessions from persistent storage
   */
  private async loadSessionsFromStorage(): Promise<void> {
    try {
      const stored = await this.getStorageItem(this.STORAGE_KEY);
      if (stored) {
        const sessionsData = JSON.parse(stored);
        this.sessions = new Map(Object.entries(sessionsData));
        console.log(`Loaded ${this.sessions.size} chatbot sessions from storage`);
      }
    } catch (error) {
      console.error('Error loading sessions from storage:', error);
    }
  }

  /**
   * Save sessions to persistent storage
   */
  private async saveSessionsToStorage(): Promise<void> {
    try {
      const sessionsData = Object.fromEntries(this.sessions);
      await this.setStorageItem(this.STORAGE_KEY, JSON.stringify(sessionsData));
    } catch (error) {
      console.error('Error saving sessions to storage:', error);
    }
  }

  /**
   * Cross-platform storage getter
   */
  private async getStorageItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        return localStorage.getItem(key);
      } else {
        return await AsyncStorage.getItem(key);
      }
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  }

  /**
   * Cross-platform storage setter
   */
  private async setStorageItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem(key, value);
      } else {
        await AsyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Storage setItem error:', error);
    }
  }
}

// Export singleton instance
export const sessionManagementService = SessionManagementService.getInstance();
export default sessionManagementService;
