# Smart Office Assistant - Environment Variables Template
# Copy this file to .env and fill in your actual values

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# App Configuration
EXPO_PUBLIC_APP_NAME=Smart Office Assistant
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=development

# Security Configuration
EXPO_PUBLIC_SESSION_TIMEOUT=3600000
EXPO_PUBLIC_MAX_LOGIN_ATTEMPTS=5
EXPO_PUBLIC_LOCKOUT_DURATION=900000

# Feature Flags
EXPO_PUBLIC_ENABLE_DEBUG_LOGGING=false
EXPO_PUBLIC_ENABLE_ERROR_REPORTING=true
EXPO_PUBLIC_ENABLE_ANALYTICS=false

# API Configuration
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_MAX_RETRY_ATTEMPTS=3

# Chatbot Configuration
EXPO_PUBLIC_CHATBOT_WEBHOOK_URL=https://n8n.taqnik.in/webhook/a2e1a26e-d2bc-4e1c-b94a-cfb56f63489e
EXPO_PUBLIC_CHATBOT_ENABLED=true
EXPO_PUBLIC_CHATBOT_TIMEOUT=60000

# Notification Configuration
EXPO_PUBLIC_NOTIFICATION_ENABLED=true

# Security Headers (for web deployment)
EXPO_PUBLIC_CSP_ENABLED=true
