-- Smart Office Assistant - Reset First-Time User Status
-- This script resets existing users to first-time status for testing onboarding flow

-- Update all existing users to be first-time users
UPDATE public.users 
SET is_first_time_user = true, 
    updated_at = NOW()
WHERE is_first_time_user = false;

-- Clear any existing onboarding completion flags from local storage
-- Note: This SQL script only handles database changes
-- Local storage needs to be cleared manually or through the app

-- Verify the changes
SELECT 
  id,
  email,
  role,
  is_first_time_user,
  updated_at
FROM public.users
ORDER BY email;

-- Show which users are now first-time users
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN is_first_time_user = true THEN 1 END) as first_time_users,
  COUNT(CASE WHEN is_first_time_user = false THEN 1 END) as returning_users
FROM public.users;
