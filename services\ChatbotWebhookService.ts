// Smart Office Assistant - Chatbot Webhook Service
// Handles webhook calls to the chatbot system with timeout and deduplication

import { configService } from './ConfigService';
import { errorLogger, ErrorCategory, ErrorSeverity } from './ErrorLoggingService';

export interface WebhookPayload {
  userId: string;
  email: string;
  fullName?: string;
  employeeId?: string;
  department?: string;
  position?: string;
  isFirstTimeUser: boolean;
  sessionId?: string;
  interactionType: 'onboarding' | 'voice_command' | 'text_response';
  isAudio?: boolean;
  timestamp: string;
}

export interface WebhookResponse {
  success: boolean;
  message?: string;
  sessionId?: string;
  error?: string;
}

interface PendingRequest {
  userId: string;
  timestamp: number;
  requestId: string;
}

class ChatbotWebhookService {
  private static instance: ChatbotWebhookService;
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly DEDUPLICATION_WINDOW = 30000; // 30 seconds
  private readonly MAX_PENDING_REQUESTS = 100;

  private constructor() {
    // Clean up old pending requests periodically
    setInterval(() => {
      this.cleanupPendingRequests();
    }, 60000); // Every minute
  }

  static getInstance(): ChatbotWebhookService {
    if (!ChatbotWebhookService.instance) {
      ChatbotWebhookService.instance = new ChatbotWebhookService();
    }
    return ChatbotWebhookService.instance;
  }

  /**
   * Send webhook notification to chatbot system
   * Includes request deduplication and 1-minute timeout
   */
  async sendWebhook(payload: WebhookPayload): Promise<WebhookResponse> {
    try {
      // Check if chatbot is enabled
      if (!configService.chatbotEnabled) {
        console.log('Chatbot webhook disabled in configuration');
        return { success: false, error: 'Chatbot webhook disabled' };
      }

      // Generate request ID for deduplication
      const requestId = this.generateRequestId(payload);
      
      // Check for duplicate request
      if (this.isDuplicateRequest(payload.userId, requestId)) {
        console.log('Duplicate webhook request detected, skipping:', requestId);
        return { success: false, error: 'Duplicate request' };
      }

      // Add to pending requests
      this.addPendingRequest(payload.userId, requestId);

      // Prepare webhook URL
      const webhookUrl = configService.chatbotWebhookUrl;
      if (!webhookUrl || webhookUrl.includes('MISSING')) {
        throw new Error('Webhook URL not configured');
      }

      console.log('Sending webhook to chatbot:', {
        url: webhookUrl,
        userId: payload.userId,
        interactionType: payload.interactionType,
        requestId
      });

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, configService.chatbotTimeout);

      // Send webhook request
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `${configService.appName}/${configService.appVersion}`,
          'X-Request-ID': requestId,
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      // Clear timeout
      clearTimeout(timeoutId);

      // Remove from pending requests
      this.removePendingRequest(payload.userId);

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`);
      }

      const result: WebhookResponse = await response.json();
      
      console.log('Webhook response received:', {
        success: result.success,
        sessionId: result.sessionId,
        requestId
      });

      return result;

    } catch (error) {
      // Remove from pending requests on error
      this.removePendingRequest(payload.userId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.error('Webhook request timed out after 1 minute');
          await errorLogger.logError('Webhook request timeout', {
            severity: ErrorSeverity.MEDIUM,
            category: ErrorCategory.NETWORK,
            context: {
              screen: 'ChatbotWebhookService',
              action: 'sendWebhook',
              additionalData: { 
                userId: payload.userId, 
                timeout: configService.chatbotTimeout,
                interactionType: payload.interactionType
              }
            }
          });
          return { success: false, error: 'Request timeout' };
        }

        console.error('Webhook request failed:', error.message);
        await errorLogger.logError(`Webhook request failed: ${error.message}`, {
          severity: ErrorSeverity.MEDIUM,
          category: ErrorCategory.NETWORK,
          context: {
            screen: 'ChatbotWebhookService',
            action: 'sendWebhook',
            additionalData: { 
              userId: payload.userId,
              error: error.message,
              interactionType: payload.interactionType
            }
          }
        });
        return { success: false, error: error.message };
      }

      return { success: false, error: 'Unknown error occurred' };
    }
  }

  /**
   * Send onboarding webhook for first-time users
   */
  async sendOnboardingWebhook(
    userId: string,
    email: string,
    employeeDetails?: {
      fullName?: string;
      employeeId?: string;
      department?: string;
      position?: string;
    },
    sessionId?: string
  ): Promise<WebhookResponse> {
    const payload: WebhookPayload = {
      userId,
      email,
      fullName: employeeDetails?.fullName,
      employeeId: employeeDetails?.employeeId,
      department: employeeDetails?.department,
      position: employeeDetails?.position,
      isFirstTimeUser: true,
      sessionId,
      interactionType: 'onboarding',
      isAudio: false,
      timestamp: new Date().toISOString(),
    };

    return this.sendWebhook(payload);
  }

  /**
   * Send voice command webhook
   */
  async sendVoiceCommandWebhook(
    userId: string,
    email: string,
    sessionId?: string
  ): Promise<WebhookResponse> {
    const payload: WebhookPayload = {
      userId,
      email,
      isFirstTimeUser: false,
      sessionId,
      interactionType: 'voice_command',
      isAudio: true,
      timestamp: new Date().toISOString(),
    };

    return this.sendWebhook(payload);
  }

  /**
   * Send text response webhook
   */
  async sendTextResponseWebhook(
    userId: string,
    email: string,
    sessionId?: string
  ): Promise<WebhookResponse> {
    const payload: WebhookPayload = {
      userId,
      email,
      isFirstTimeUser: false,
      sessionId,
      interactionType: 'text_response',
      isAudio: false,
      timestamp: new Date().toISOString(),
    };

    return this.sendWebhook(payload);
  }

  /**
   * Generate unique request ID for deduplication
   */
  private generateRequestId(payload: WebhookPayload): string {
    const data = `${payload.userId}-${payload.interactionType}-${payload.timestamp}`;
    return Buffer.from(data).toString('base64').slice(0, 16);
  }

  /**
   * Check if request is duplicate within deduplication window
   */
  private isDuplicateRequest(userId: string, requestId: string): boolean {
    const existing = this.pendingRequests.get(userId);
    if (!existing) return false;

    const now = Date.now();
    const isWithinWindow = (now - existing.timestamp) < this.DEDUPLICATION_WINDOW;
    const isSameRequest = existing.requestId === requestId;

    return isWithinWindow && isSameRequest;
  }

  /**
   * Add request to pending requests map
   */
  private addPendingRequest(userId: string, requestId: string): void {
    // Prevent memory leaks by limiting pending requests
    if (this.pendingRequests.size >= this.MAX_PENDING_REQUESTS) {
      this.cleanupPendingRequests();
    }

    this.pendingRequests.set(userId, {
      userId,
      timestamp: Date.now(),
      requestId,
    });
  }

  /**
   * Remove request from pending requests map
   */
  private removePendingRequest(userId: string): void {
    this.pendingRequests.delete(userId);
  }

  /**
   * Clean up old pending requests
   */
  private cleanupPendingRequests(): void {
    const now = Date.now();
    const expiredThreshold = now - this.DEDUPLICATION_WINDOW;

    for (const [userId, request] of this.pendingRequests.entries()) {
      if (request.timestamp < expiredThreshold) {
        this.pendingRequests.delete(userId);
      }
    }
  }

  /**
   * Get current pending requests count (for monitoring)
   */
  getPendingRequestsCount(): number {
    return this.pendingRequests.size;
  }

  /**
   * Clear all pending requests (for testing/cleanup)
   */
  clearPendingRequests(): void {
    this.pendingRequests.clear();
  }
}

// Export singleton instance
export const chatbotWebhookService = ChatbotWebhookService.getInstance();
export default chatbotWebhookService;
